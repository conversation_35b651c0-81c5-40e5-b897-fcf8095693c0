<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Alienor\ApiBundle\Services\FlashMessages;
use Eduprat\DomainBundle\Services\SearchHandler;
use Eduprat\DomainBundle\Entity\LogoPartenaire;
use Ed<PERSON>rat\DomainBundle\Form\LogoPartenaireType;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Eduprat\DomainBundle\Controller\EdupratController;

#[Route(path: '/logo-partenaire')]
#[IsGranted('ROLE_COORDINATOR')]
class LogoPartenaireController extends EdupratController
{
    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler)
    {
        parent::__construct($flashMessages, $searchHandler);
    }

    /**
     * Liste des logos partenaires
     */
    #[Route(path: '/', name: 'admin_logo_partenaire_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $logoPartenaireRepository = $em->getRepository(LogoPartenaire::class);
        $logos = $logoPartenaireRepository->findAll();

        return $this->render('admin/logo_partenaire/index.html.twig', [
            'logos' => $logos
        ]);
    }

    /**
     * Création d'un logo partenaire
     */
    #[Route(path: '/create', name: 'admin_logo_partenaire_create')]
    public function create(Request $request, EntityManagerInterface $entityManager): Response
    {
        $logoPartenaire = new LogoPartenaire();

        $form = $this->createForm(LogoPartenaireType::class, $logoPartenaire);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $logoPartenaire->setPersonUploader($this->getUser());
            $entityManager->persist($logoPartenaire);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.logoPartenaire.create.success');
            return $this->redirectToRoute('admin_logo_partenaire_index');
        }

//        dd($form->getErrors(true));

        return $this->render('admin/logo_partenaire/create.html.twig', [
            'form' => $form->createView()
        ]);
    }

    /**
     * Édition d'un logo partenaire
     */
    #[Route(path: '/{id}/edit', name: 'admin_logo_partenaire_edit')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function edit(Request $request, LogoPartenaire $logoPartenaire, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(LogoPartenaireType::class, $logoPartenaire, [
            'edit' => true
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.logoPartenaire.edit.success');
            return $this->redirectToRoute('admin_logo_partenaire_index');
        }

        return $this->render('admin/logo_partenaire/edit.html.twig', [
            'form' => $form->createView(),
            'logo' => $logoPartenaire
        ]);
    }

    /**
     * Création d'un logo partenaire via AJAX
     */
    #[Route(path: '/create-ajax', name: 'admin_logo_partenaire_create_ajax', methods: ['POST'])]
    public function createAjax(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $logoPartenaire = new LogoPartenaire();

        $form = $this->createForm(LogoPartenaireType::class, $logoPartenaire);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $logoPartenaire->setPersonUploader($this->getUser());
            $entityManager->persist($logoPartenaire);
            $entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => 'Logo partenaire créé avec succès',
                'logo' => [
                    'id' => $logoPartenaire->getId(),
                    'logoName' => $logoPartenaire->getLogoName(),
                    'relativeUrl' => $logoPartenaire->getRelativeUrl()
                ]
            ]);
        }

        // Récupération des erreurs du formulaire
        $errors = [];
        foreach ($form->getErrors(true) as $error) {
            $errors[] = $error->getMessage();
        }

        return $this->json([
            'success' => false,
            'message' => 'Erreur lors de la création du logo partenaire',
            'errors' => $errors
        ], 400);
    }

    /**
     * Suppression d'un logo partenaire
     */
    #[Route(path: '/{id}/delete', name: 'admin_logo_partenaire_delete')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function delete(Request $request, LogoPartenaire $logoPartenaire, EntityManagerInterface $entityManager): RedirectResponse
    {

        $entityManager->remove($logoPartenaire);

        $entityManager->flush();

        $this->flashMessages->addSuccess('admin.logoPartenaire.delete.success');
        return $this->redirectToRoute('admin_logo_partenaire_index');
    }
}