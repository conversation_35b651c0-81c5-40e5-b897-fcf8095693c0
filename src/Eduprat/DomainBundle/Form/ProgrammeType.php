<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Ed<PERSON>rat\DomainBundle\Entity\Programme;
use Ed<PERSON>rat\DomainBundle\Entity\Tag;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Vich\UploaderBundle\Form\Type\VichImageType;
use Eduprat\AdminBundle\Services\ProgrammeBuilder;

class ProgrammeType extends AbstractType
{

    private ParameterBagInterface $parameterBag;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $this->parameterBag = $parameterBag;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $lastYear = ((int) date("Y")) + 1;
        $years = range(2017, $lastYear);

        $data = null;
        $guidedType = ProgrammeBuilder::isGuidedFormation($options["data"]);

        $builder
            ->add('title', null, array(
                'label' => 'admin.formation.title',
            ))
            ->add('reference', null, array(
                'label' => 'admin.formation.reference',
            ))
            ->add('cout', null, array(
                'label' => 'admin.formation.cout',
            ))
            ->add('year', ChoiceType::class, array(
                'label' => 'admin.programme.year',
                'required' => true,
                'choices' => array_combine($years, $years),
            ))
            ->add('andpcStatus', ChoiceType::class, array(
                'label' => 'admin.programme.andpcStatus',
                'required' => false,
                'choices' => array_flip(Programme::ANDPC_STATUSES),
            ))
            ->add('categories', ChoiceType::class, array(
                'label' => 'admin.formation.categories.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => self::getCategories(),
                'placeholder' => 'admin.formation.categories.empty',
            ))
            ->add('specialities', ChoiceType::class, array(
                'label' => 'admin.formation.specialities.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => self::getSpecialities(),
                'placeholder' => 'admin.formation.specialities.empty',
            ))
            ->add('exercisesMode', ChoiceType::class, array(
                'label' => 'admin.formation.exerciseMode.title',
                'expanded' => false,
                'multiple' => true,
                'required' => true,
                'constraints' => array(
                    new NotBlank()
                ),
                'choices'  => self::getExerciseModes(),
                'placeholder' => 'admin.formation.exerciseMode.empty',
            ))

            ->add('tempTags', HiddenType::class, array(
                'data' => '',
                'required' => false
            ))
            ->add('programmesAssocies', ProgrammesAssociesType::class, array(
            ))

        //    ->add('coordinators', CollectionType::class, array(
        //        'required' => true,
        //        'label' => false,
        //        'entry_options' => array(
        //            'label' => false
        //        ),
        //        'entry_type' => CoordinatorType::class,
        //        'allow_add'    => true,
        //        'allow_delete'    => true,
        //        'by_reference' => false,
        //        'error_bubbling' => false,
        //    ))
        //     ->add('formateurs', CollectionType::class, array(
        //         'required' => false,
        //         'label' => false,
        //         'entry_options' => array(
        //             'label' => false
        //         ),
        //         'entry_type' => FormateurType::class,
        //         'allow_add'    => true,
        //         'allow_delete'    => true,
        //         'by_reference' => false,
        //         'error_bubbling' => false,
        //     ))
        //     ->add('costKilometres', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.costKilometresLabel',
        //     ))
        //     ->add('costBadges', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.costBadges',
        //     ))
        //     ->add('costRetrocessions', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.costRetrocessions',
        //     ))
        //     ->add('costMateriel', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.costMateriel',
        //     ))
        //     ->add('costDivers', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.costDivers',
        //     ))
        //     ->add('messageRappel', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.messageRappel',
        //     ))
        //     ->add('messageRappel', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.messageRappel',
        //     ))
        //     ->add('address', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.address.title',
        //     ))
        //     ->add('address2', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.address2.title',
        //     ))
        //     ->add('city', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.city.empty',
        //     ))
        //     ->add('zipCode', null, array(
        //         'required' => false,
        //         'label' => 'admin.formation.zipCode.empty',
        //     ))
        //     ->add('startDate', DateTimeType::class, array(
        //         'format' => 'dd/MM/yyyy',
        //         'widget' => 'single_text',
        //         'html5' => false,
        //         'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
        //         'label' => 'admin.formation.startDate',
        //     ))
        //     ->add('startTime', TimeType::class, array(
        //         'input'  => 'datetime',
        //         'widget' => 'choice',
        //         'mapped' => false,
        //         'label' => 'admin.formation.startTime',
        //     ))
        //     ->add('endDate', DateTimeType::class, array(
        //         'format' => 'dd/MM/yyyy',
        //         'widget' => 'single_text',
        //         'html5' => false,
        //         'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
        //         'label' => 'admin.formation.endDate',
        //         'required' => false,
        //     ))
        //     ->add('endTime', TimeType::class, array(
        //         'input'  => 'datetime',
        //         'widget' => 'choice',
        //         'mapped' => false,
        //         'label' => 'admin.formation.endTime',
        //         'required' => false,
        //     ))
            ->add('resume', null, array(
                'required' => false,
                'label' => 'admin.formation.resume',
            ))
            ->add('prerequis', null, array(
                'required' => true,
                'label' => 'admin.formation.prerequis',
            ))
            ->add('commentaireNotif', null, array(
                'required' => false,
                'label' => 'admin.formation.commentaireNotif',
            ))
            ->add('objectives', null, array(
                'required' => false,
                'label' => 'admin.formation.objectivesProgramme',
                'attr' => array(
                    'class' => 'tinymce',
                ),
            ))
            ->add('objectivesActalians', null, array(
                'required' => false,
                'label' => 'admin.programme.objectivesActalians',
                'attr' => array(
                    'class' => 'tinymce',
                ),
            ))
            ->add('nationalOrientation', null, array(
                'required' => false,
                'label' => 'admin.programme.orientationProgramme',
                'attr' => array(
                    'class' => 'tinymce',
                ),
            ))
            ->add('additionalInfos', null, array(
                'required' => false,
                'label' => 'admin.programme.additionalInfos',
                'attr' => array(
                    'class' => 'tinymce',
                ),
            ))
            ->add('pictureFile', VichImageType::class, array(
                'label' => 'admin.formation.picture',
                'required' => false,
                'allow_delete' => !$options["copy"],
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400)
            ))
            ->add('pictureFrom', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false
            ))
            ->add('firstAdditionalInfosPictureFile', VichImageType::class, array(
                'label' => 'admin.formation.firstAdditionalInfosPicture',
                'required' => false,
                'allow_delete' => !$options["copy"],
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400)
            ))
            ->add('firstAdditionalInfosPictureFrom', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false
            ))
            ->add('secondAdditionalInfosPictureFile', VichImageType::class, array(
                'label' => 'admin.formation.secondAdditionalInfosPicture',
                'required' => false,
                'allow_delete' => !$options["copy"],
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400)
            ))
            ->add('secondAdditionalInfosPictureFrom', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false
            ))
            ->add('firstAdditionalInfosPictureLink', null, array(
                'required' => false,
                'label' => 'admin.formation.firstAdditionalInfosPictureLink',
            ))
            ->add('secondAdditionalInfosPictureLink', null, array(
                'required' => false,
                'label' => 'admin.formation.secondAdditionalInfosPictureLink',
            ))
            ->add('prisesEnCharge', EntityType::class, array(
                'label' => 'admin.formation.prisesEnCharge',
                'class' => PriseEnCharge::class,
                'choice_label' => function (PriseEnCharge $priseEnCharge) {
                    return $priseEnCharge->getName();
                },
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('pec')
                        ->orderBy('pec.name', 'ASC');
                },
                'expanded' => false,
                'multiple' => true,
                'required' => true
            ))
            ->add('certifying', CheckboxType::class, array(
                'label' => 'admin.formation.certifying',
                'required' => false
            ))
            ->add('category', EntityType::class, array(
                'class' => 'Eduprat\DomainBundle\Entity\AuditCategory',
                'label'    => 'admin.programme.category.label',
                'choice_label' => 'name',
                'expanded' => false,
                'multiple' => false,
                'placeholder' => 'admin.programme.category.empty',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('c')
                        ->orderBy('c.name', 'ASC');
                },
            ))
            ->add('sessionType', ChoiceType::class, array(
                'label' => 'admin.programme.sessionType.label',
                'required' => true,
                'placeholder' => 'admin.programme.sessionType.empty',
                'choices' => self::getSessionTypes($guidedType),
                'choice_attr' => function($v) {
                    $forms = array("survey");
                    if ($v === "formation_audit") {
                        $forms = array("audit", "predefined");
                    } else if ($v === "formation_vignette") {
                        $forms = array("vignette");
                    }
                    else if($v === "formation_elearning") {
                        $forms = array("survey", "audit", "predefined", "vignette", "vignette_audit", "audit_vignette");
                    }
                    else if($v === "formation_vignette_audit") {
                        $forms = array("vignette_audit", "audit_vignette");
                    }
                    else if($v === Formation::TYPE_VFC) {
                        $forms = array("vignette");
                    }
                    else if($v === Formation::TYPE_TCS) {
                        $forms = array("tcs");
                    }
                    return array(
                        "data-form" => join(",", $forms)
                    );
                },
                'disabled' => $guidedType
            ))
            ->add('presence', ChoiceType::class, array(
                'label' => 'admin.programme.presence',
                'required' => true,
                'placeholder' => 'admin.programme.formType.empty',
                'choices' => self::getPresences(),
                'attr' => array(
                    'class' => 'presence'
                ),
                'choice_attr' => function($c) {
                    return $this->getPresencesInfos($c);
                },
                'disabled' => $guidedType
            ))
            ->add('formType', ChoiceType::class, array(
                'label' => 'admin.programme.formType.label',
                'required' => true,
                'placeholder' => 'admin.programme.formType.empty',
                'choices' => self::getFormTypes(),
                'disabled' => $guidedType
            ))
            ->add('durationNotPresentielle', null, array(
                'required' => false,
                'label' => 'admin.formation.durationNotPresentielle',
                'label_attr' => array(
                    'class' => 'actalians-label',
                    'data-original' => 'Étape 1 : nombre d\'heures non   présentielles',
                    'data-actalians' => 'Étape 1 : Durée non présentielle (nb heures) :'
                ),
                'attr' => array (
                    'class' => "durationNotPresentielle",
                ),
                'disabled' => true,
            ))
            ->add('durationPresentielle', null, array(
                'required' => false,
                'label' => 'admin.formation.durationPresentielle',
                'label_attr' => array(
                    'class' => 'actalians-label',
                    'data-original' => 'Étape 2 : nombre d\'heures   présentielles',
                    'data-actalians' => 'Étape 2 : Durée présentielle (nb heures) :'
                ),
                'attr' => array (
                    'class' => "durationPresentielle",
                ),
                'disabled' => true,
            ))
            ->add('durationNotPresentielleActalians', null, array(
                'required' => false,
                'label' => 'admin.programme.durationNotPresentielleActalians',
                'label_attr' => array(
                    'class' => 'actalians-label',
                    'data-original' => 'Étape 3 : nombre d\'heures non   présentielles',
                    'data-actalians' => 'Étape 3 : Durée non présentielle (nb heures) :'
                )
            ))
            // ->add('autocompleteReference', CheckboxType::class, array(
            //     'label' => 'admin.formation.autocompleteReference',
            //     'required' => false
            // ))
            ->add('tags', EntityType::class, array(
                'label' => 'admin.formation.tags',
                'class' => Tag::class,
                'choice_label' => function (Tag $tag) {
                    return $tag->getName();
                },
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('t')
                        ->orderBy('t.name', 'ASC');
                },
                'expanded' => false,
                'multiple' => true,
                'required' => false
            ))
            ->add('connaissances', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => ConnaissanceType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
           ))
           ->add('competences', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => CompetenceType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
            ))
            ->add('unities', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false,
                    'guidedType' => $guidedType
                ),
                'entry_type' => UnityFormationType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
           ))
            ->add('linkVideoModule1', null, array(
                'required' => true,
                'label' => 'admin.programme.linkVideoModule1',
            ))
            ->add('save', SubmitType::class, array(
                'label' => 'login.save',
                'attr' => array(
                    'class' => 'btn btn-eduprat'
                ),
            ))
        ;

        // $builder->addEventListener(
        //     FormEvents::POST_SET_DATA,
        //     function (FormEvent $event) {
        //         /** @var Programme $data */
        //         $data = $event->getData();
        //         $event->getForm()->get('startTime')->setData($data->getStartDate());
        //         $event->getForm()->get('endTime')->setData($data->getEndDate());
        //     }
        // );

        /** Bidouillage pour regrouper les heures dans le champs date */
        // $builder->addEventListener(
        //     FormEvents::PRE_SUBMIT,
        //     function (FormEvent $event) {
        //         $data = $event->getData();

        //         /** @var \DateTime $hours */
        //         $startDate = \DateTime::createFromFormat('d/m/Y', strtok($data["startDate"], ' '));
        //         $field = $event->getForm()->get('startDate');
        //         $config  = $field->getConfig();
        //         $options = $config->getOptions();
        //         $event->getForm()->add(
        //             $field->getName(),
        //             get_class($config->getType()->getInnerType()),
        //             array_replace($options, ['format' => 'dd/MM/yyyy HH:mm'])
        //         );
        //         if ($startDate) {
        //             $startDate->setTime($data["startTime"]["hour"], $data["startTime"]["minute"]);
        //             $data["startDate"] = $startDate->format('d/m/Y H:i');
        //         }

        //         /** @var \DateTime $hours */
        //         $endDate = \DateTime::createFromFormat('d/m/Y', strtok($data["endDate"], ' '));
        //         $field = $event->getForm()->get('endDate');
        //         $config  = $field->getConfig();
        //         $options = $config->getOptions();
        //         $event->getForm()->add(
        //             $field->getName(),
        //             get_class($config->getType()->getInnerType()),
        //             array_replace($options, ['format' => 'dd/MM/yyyy HH:mm'])
        //         );
        //         if ($endDate) {
        //             $endDate->setTime($data["endTime"]["hour"], $data["endTime"]["minute"]);
        //             $data["endDate"] = $endDate->format('d/m/Y H:i');
        //         }

        //         $event->setData($data);
        //     }
        // );
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\Programme',
            'exercisesMode' => null,
            'copy' => false,
            'guidedType' => false,
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_programme';
    }

    /**
     * @return array
     */
    public static function getExerciseModes()
    {
        return array(
            'Monomode' => array(
                'Libéral' => 'Libéral',
                'Salarié' => 'Salarié',
                'Salarié en centre de santé conventionné' => 'Salarié en centre de santé conventionné',
                'Salarié hospitalier' => 'Salarié hospitalier',
                'Salarié de l’industrie' => 'Salarié de l’industrie'
            ),
            'Monomode 2017' => array(
                'Mixte' => 'Mixte',
                'Salarié' => 'Salarié',
                'Service de Santé des Armées' => 'Service de Santé des Armées'
            )
        );
    }

    /**
     * @return array
     */
    public static function getFormats()
    {
        return array(
            'Audit' => 'Audit',
            'Présentielle' => 'Présentielle',
            'Non présentiel' => 'E-Learning',
            'SEDD' => 'SEDD',
            'SET' => 'SET',
            'Congrés' => 'Congrés',
            'Mixte' => 'Mixte'
        );
    }

    /**
     * @return array
     */
    public static function getPresences()
    {
        return array(
            Programme::PRESENCE_SITE => Programme::PRESENCE_SITE,
            Programme::PRESENCE_VIRTUELLE => Programme::PRESENCE_VIRTUELLE,
            Programme::PRESENCE_ELEARNING => Programme::PRESENCE_ELEARNING,
        );
    }

    /**
     * @return array
     */
    public function getPresencesInfos($presence)
    {
        $infos = array();
        switch ($presence) {
            case Programme::PRESENCE_SITE:
                $infos["additionalInfos"] = '';
                $infos["firstAdditionalInfosPictureFrom"] = null;
                $infos["secondAdditionalInfosPictureFrom"] = null;
                $infos["firstAdditionalInfosPictureLink"] = null;
                $infos["secondAdditionalInfosPictureLink"] = null;
                break;
            case Programme::PRESENCE_VIRTUELLE:
                $infos["additionalInfos"] = '<p style="text-align: center;"><strong>LES CLASSES VIRTUELLES EDUPRAT</strong></p>
<div style="text-align: center;"><strong>1-&nbsp;</strong>Je t&eacute;l&eacute;charge et j\'installe ZOOM</div>
<div style="text-align: center;"><em>(Sur ordinateur ou sur tablette)</em></div>
<div style="text-align: center;">&nbsp;</div>
<div style="text-align: center;"><strong>2-</strong>&nbsp;Le jour de ma formation, r&eacute;ception d\'un mail,</div>
<div style="text-align: center;">avec les informations de connexion</div>
<div style="text-align: center;">&nbsp;</div>
<div style="text-align: center;"><span style="text-decoration: underline;">Au moment de la formation :</span></div>
<div style="text-align: center;">Ouvrir Zoom</div>
<div style="text-align: center;">Saisir l\'ID de r&eacute;union</div>
<div style="text-align: center;">Saisir votre NOM Pr&eacute;nom</div>
<p style="text-align: center;"><strong>LA FORMATION LIVE PEUT COMMENCER</strong></p>';
        }
        return array(
            "data-infos" => json_encode($infos)
        );
    }

    /**
     * @return array
     */
    public static function getPriseEnCharge()
    {
        $array = array(
            "DPC",
            "FIF-PL",
            "FAF-PM",
            "OPCO EP",
            "Personnel"
        );
        return array_combine($array, $array);
    }

    public static function getGroupSpecialities($withGroup = false, $forPlaquette = false): array
    {
        $medecins = array(
            "Médecine généraliste" => ["Médecine générale"],
            "Cardiologue" => [
                // "Cardiologie et maladies vasculaires / Pathologies cardio-vasculaire",
                "Médecine cardiovasculaire",
            ],
            "Dermatologue" => [
                "Dermatologie et vénéréologie",
            ],
            "Gynécologue" =>[
                "Gynécologie médicale",
                // "Gynécologie médicale et obstétrique",
                "Gynécologie obstétrique",
            ],
            'Pédiatre' => ['Pédiatrie'],
            'Neurologue' => ['Neurologie'],
            'Urologue' => ['Urologie'],
            'Ophalmologue' => ["Ophtalmologie"],
            'Médecin vasculaire' => ["Médecine vasculaire"],
            "Rhumatologue" => ["Rhumatologie"],
            "Urgentiste" => ["Médecine d’urgence"],
            "Psychiatre" => [
                "Psychiatrie",
                "Psychiatrie de l’enfant et de l’adolescent",
            ],
            "Autre" => [
                "Allergologie",
                "Anesthésie-réanimation",
                "Anatomie et cytologie pathologiques",
                "Chirurgie de la face et du cou",
                "Chirurgie générale",
                "Chirurgie infantile",
                "Chirurgie maxillo-faciale",
                "Chirurgie maxillo-faciale et stomatologie",
                "Chirurgie orale",
                "Chirurgie orthopédique et traumatologique",
                "Chirurgie plastique reconstructrice et esthétique",
                "Chirurgie pédiatrique",
                "Chirurgie thoracique et cardio-vasculaire",
                "Chirurgie urologique",
                "Chirurgie vasculaire",
                "Chirurgie viscérale et digestive",
                // "Endocrinologie et métabolismes",
                // "Endocrinologie diabétologie et maladies métaboliques",
                "Endocrinologie-diabétologie-nutrition",
                // "Gastro-entérologie et hépatologie",
                "Génétique médicale",
                "Gériatrie / Gérontologie",
                "Hématologie",
                "Hépato-gastro-entérologie",
                "Maladies infectieuses et tropicales",
                "Médecine et santé au travail",
                "Médecine intensive-réanimation",
                "Médecine interne et immunologie clinique",
                "Médecine légale et expertises médicale",
                "Médecine nucléaire",
                "Médecine physique et réadaptation",
                "Neurochirurgie",
                // "Neuropsychiatrie",
                "Néphrologie",
                "Oncologie",
                // "Oncologie radiothérapique",
                "Oto-rhino-laryngologie - chirurgie cervico-faciale",
                "Pneumologie",
                // "Radiodiagnostic et imagerie médicale",
                "Radiologie et imagerie médicale",
                // "Radiothérapie",
                // "Réanimation médicale",
                "Santé publique",
                // "Santé publique et médecine sociale",
                // "Stomatologie",
                "Médecin PMI"
            ],
        );
        $infirmiers = array(
            'Infirmier' => [
                "Aide-soignant",
                "Infirmier Anesthésiste Diplômé d’Etat (IADE)",
                "Infirmier de Bloc Opératoire Diplômé d’Etat (IBODE)",
                "Infirmier en pratique avancée (IPA)",
                "Infirmier Diplômé d’Etat (IDE)",
                "Infirmier Puéricultrice Diplômée d’Etat",
                "Auxiliaire de puériculture",
            ]
        );
        $masseurkinesitherapeute = array('Masseur-kinésithérapeute'=> ['Masseur-kinésithérapeute'],);
        $pharmacien = array('Pharmacien' => ["Pharmacien adjoint d’officine", "Pharmacien hospitalier", "Pharmacien titulaire d’officine",]);
        $preparateurEnPharmacie = array('Préparateur en pharmacie' => ['Préparateur en pharmacie'],);
        $sageFemme = array('Sage-Femme' => ['Sage-Femme'],);
        $churugienDentiste = array(
            'Dentiste' => [
                "Chirurgie dentaire (specialiste Orthopédie Dento-Faciale)",
                "Chirurgie dentaire (omnipraticiens)",
                "Chirurgie dentiste spécialisé en chirurgie orale",
                "Chirurgie dentiste spécialisé en médecine bucco dentaire",
            ]
        );
        $biologiste = array('Biologiste' => ["Biologie médicale",]);

        $aideSoignant = ['Aide-soignant' => ["Aide-soignant"]];
        $audioprothesiste = ['Audioprothésiste' => ["Audioprothésiste"]];
        $dieteticien = ['Diététicien' => ["Diététicien"]];
        $ergotherapeute = ['Ergothérapeute' => ["Ergothérapeute"]];
        $opticienLunetier = ['Opticien-lunetier' => ["Opticien-lunetier"]];
        $orthesiste = ['Orthésiste' => ["Orthésiste"]];
        $orthopedisteOrthesiste = ['Orthopédiste-orthésiste' => ["Orthopédiste-orthésiste"]];
        $orthophoniste = ['Orthophoniste' => ["Orthophoniste"]];
        $orthoprothesiste = ['Orthoprothésiste' => ["Orthoprothésiste"]];
        $orthoptiste = ['Orthoptiste' => ["Orthoptiste"]];
        $pedicurePodologue = ['Pédicure-podologue' => ["Pédicure-podologue"]];
        $podoOrthesiste = ['Podo-orthésiste' => ["Podo-orthésiste"]];
        $prothesiste = ['Prothésiste' => ["Prothésiste"]];
        $psychomotricien = ['Psychomotricien' => ["Psychomotricien"]];

        if (!$forPlaquette) {
            $medecins["Cardiologue"][] = "Cardiologie et maladies vasculaires / Pathologies cardio-vasculaire";
            $medecins["Gynécologue"][] = "Gynécologie médicale et obstétrique";
            $medecins["Autre"][] = "Endocrinologie et métabolismes";
            $medecins["Autre"][] = "Endocrinologie diabétologie et maladies métaboliques";
            $medecins["Autre"][] = "Gastro-entérologie et hépatologie";
            $medecins["Autre"][] = "Neuropsychiatrie";
            $medecins["Autre"][] = "Oncologie radiothérapique";
            $medecins["Autre"][] = "Radiodiagnostic et imagerie médicale";
            $medecins["Autre"][] = "Radiothérapie";
            $medecins["Autre"][] = "Réanimation médicale";
            $medecins["Autre"][] = "Santé publique et médecine sociale";
            $medecins["Autre"][] = "Stomatologie";
        }

        if (!$withGroup) {
            return [
                'Médecin' => array_merge(...array_values($medecins)),
                'infirmiers' => array_merge(...array_values($infirmiers)),
                'masseurkinesitherapeute' => array_merge(...array_values($masseurkinesitherapeute)),
                'pharmacien' => array_merge(...array_values($pharmacien)),
                'preparateurEnPharmacie' => array_merge(...array_values($preparateurEnPharmacie)),
                'sageFemme' => array_merge(...array_values($sageFemme)),
                'churugienDentiste' => array_merge(...array_values($churugienDentiste)),
                'biologiste' => array_merge(...array_values($biologiste)),
                'aideSoignant' => array_merge(...array_values($aideSoignant)),
                'audioprothesiste' => array_merge(...array_values($audioprothesiste)),
                'dieteticien' => array_merge(...array_values($dieteticien)),
                'ergotherapeute' => array_merge(...array_values($ergotherapeute)),
                'opticienLunetier' => array_merge(...array_values($opticienLunetier)),
                'orthesiste' => array_merge(...array_values($orthesiste)),
                'orthopedisteOrthesiste' => array_merge(...array_values($orthopedisteOrthesiste)),
                'orthophoniste' => array_merge(...array_values($orthophoniste)),
                'orthoprothesiste' => array_merge(...array_values($orthoprothesiste)),
                'orthoptiste' => array_merge(...array_values($orthoptiste)),
                'pedicurePodologue' => array_merge(...array_values($pedicurePodologue)),
                'podoOrthesiste' => array_merge(...array_values($podoOrthesiste)),
                'prothesiste' => array_merge(...array_values($prothesiste)),
                'psychomotricien' => array_merge(...array_values($psychomotricien)),
            ];
        }
        return [
            'Médecin' => $medecins,
            'Infirmier' => $infirmiers,
            'Masseur-kinésithérapeute' => $masseurkinesitherapeute,
            'Pharmacien' => $pharmacien,
            'Préparateur en pharmacie' => $preparateurEnPharmacie,
            'Sage-Femme' => $sageFemme,
            'Chirurgien-dentiste' => $churugienDentiste,
            'Biologiste' => $biologiste,
            'Aide-soignant' => $aideSoignant,
            'Audioprothésiste' => $audioprothesiste,
            'Diététicien' => $dieteticien,
            'Ergothérapeute' => $ergotherapeute,
            'Opticien-lunetier' => $opticienLunetier,
            'Orthésiste' => $orthesiste,
            'Orthopédiste-orthésiste' => $orthopedisteOrthesiste,
            'Orthophoniste' => $orthophoniste,
            'Orthoprothésiste' => $orthoprothesiste,
            'Orthoptiste' => $orthoptiste,
            'Pédicure-podologue' => $pedicurePodologue,
            'Podo-orthésiste' => $podoOrthesiste,
            'Prothésiste' => $prothesiste,
            'Psychomotricien' => $psychomotricien,
        ];
    }

    /**
     * @return array
     */
    public static function getSpecialities($forPlaquette = false)
    {
        $specialities = self::getGroupSpecialities(false, $forPlaquette);
        $medecins = $specialities['Médecin'];
        $pharmacien = $specialities['pharmacien'];
        $preparateur = $specialities['preparateurEnPharmacie'];
        $chirurgien = $specialities['churugienDentiste'];
        $sageFemme = $specialities['sageFemme'];
        $infirmiers = $specialities['infirmiers'];
        $masseur = $specialities['masseurkinesitherapeute'];
        $orthophoniste = $specialities['orthophoniste'];
        $orthoptiste = $specialities['orthoptiste'];
        $audioprothesiste = $specialities['audioprothesiste'];
        $orthoprothesiste = $specialities['orthoprothesiste'];
        $opticienlunetier = $specialities['opticienLunetier'];
        $cadredesante = array("Cadre de santé");
        $psychotherapeute = array("Psychothérapeute");
        $secretaire = array("Secrétaire");
        $psychologue = array("Psychologue");
        $autre = array("Autre");
        $orthopedisteOrthesiste = $specialities['orthopedisteOrthesiste'];
        $orthesiste = $specialities['orthesiste'];
        $podoorthesiste = $specialities['podoOrthesiste'];
        $prothesiste = $specialities['prothesiste'];
        $podologue = $specialities['pedicurePodologue'];
        $dieteticien = $specialities['dieteticien'];
        $ergo = $specialities['ergotherapeute'];
        $biologiste = $specialities['biologiste'];
        $psychomotricien = $specialities['psychomotricien'];
        $manipulateur = array("Manipulateur d’électroradiologie médicale (ERM)");
        $technicien = array("Technicien de laboratoire médical");
        $chiropracteur = array("Chiropracteur");
        $industrie = array("Industrie");
        $ostheopathe = array("Ostéopathe");
        $educateur =  array("Educateur");
        $accompagnantEducatif =  array("Accompagnant éducatif et social");
        $agentServiceHospitalier =  array("Agent de Services Hospitaliers");
        $aideMedicoLegale =  array("Aide médico-psychologique");
        $personnelSoignant =  array("Personnel soignant");
        $auxiliaire =  array("Auxiliaire de vie");
        $etudiant = array_map(function($etudiantsSpeciality) {
            return "Etudiant - " . $etudiantsSpeciality;
        }, array_merge(array("Interne","Externe"), $medecins, $pharmacien, $preparateur, $chirurgien, $sageFemme, $infirmiers, $masseur, $orthophoniste, $orthoptiste, $audioprothesiste, $orthoprothesiste, $opticienlunetier, $cadredesante, $psychotherapeute, $secretaire, $psychologue, $orthopedisteOrthesiste, $orthesiste, $podoorthesiste, $prothesiste, $podologue, $dieteticien, $ergo, $biologiste, $psychomotricien, $manipulateur, $technicien, $chiropracteur, $industrie, $psychologue, $ostheopathe, $educateur, $autre));

        return array(
            "Accompagnant éducatif et social" => array_combine($accompagnantEducatif, $accompagnantEducatif),
            "Agent de Services Hospitaliers" => array_combine($agentServiceHospitalier, $agentServiceHospitalier),
            "Aide médico-psychologique" => array_combine($aideMedicoLegale, $aideMedicoLegale),
            "Audioprothésiste" => array_combine($audioprothesiste, $audioprothesiste),
            "Auxiliaire de vie " => array_combine($auxiliaire, $auxiliaire),
            "Biologiste" => array_combine($biologiste, $biologiste),
            "Cadre de santé" => array_combine($cadredesante, $cadredesante),
            "Chirurgien-dentiste" => array_combine($chirurgien, $chirurgien),
            "Chiropracteur" => array_combine($chiropracteur, $chiropracteur),
            "Diététicien" => array_combine($dieteticien, $dieteticien),
            "Educateur" => array_combine($educateur, $educateur),
            "Ergothérapeute" => array_combine($ergo, $ergo),
            "Industrie" => array_combine($industrie, $industrie),
            "Infirmier" => array_combine($infirmiers, $infirmiers),
            "Manipulateur d’électroradiologie médicale (ERM)" => array_combine($manipulateur, $manipulateur),
            "Masseur-kinésithérapeute" => array_combine($masseur, $masseur),
            "Médecin" => array_combine($medecins, $medecins),
            "Opticien-lunetier" => array_combine($opticienlunetier, $opticienlunetier),
            "Orthophoniste" => array_combine($orthophoniste, $orthophoniste),
            "Orthoprothésiste" => array_combine($orthoprothesiste, $orthoprothesiste),
            "Orthoptiste" => array_combine($orthoptiste, $orthoptiste),
            "Orthopédiste-orthésiste" => array_combine($orthopedisteOrthesiste, $orthopedisteOrthesiste),
            "Orthésiste" => array_combine($orthesiste, $orthesiste),
            "Ostéopathe" => array_combine($ostheopathe, $ostheopathe),
            "Pédicure-podologue" => array_combine($podologue, $podologue),
            "Personnel soignant" => array_combine($personnelSoignant, $personnelSoignant),
            "Pharmacien" => array_combine($pharmacien, $pharmacien),
            "Podo-orthésiste" => array_combine($podoorthesiste, $podoorthesiste),
            "Prothésiste" => array_combine($prothesiste, $prothesiste),
            "Préparateur en pharmacie" => array_combine($preparateur, $preparateur),
            "Psychothérapeute" => array_combine($psychotherapeute, $psychotherapeute),
            "Psychologue" => array_combine($psychologue, $psychologue),
            "Psychomotricien" => array_combine($psychomotricien, $psychomotricien),
            "Sage-Femme" => array_combine($sageFemme, $sageFemme),
            "Secrétaire" => array_combine($secretaire, $secretaire),
            "Technicien de laboratoire médical" => array_combine($technicien, $technicien),
            "Etudiant" => array_combine($etudiant, $etudiant),
            "Autre" => array_combine($autre, $autre),
        );
    }

    public static function getSessionTypes($guidedTypeOrDisplayNew = true) {
        $sessionTypes = array (
            "Audit - Cas cliniques" => Formation::TYPE_AUDIT,
            "admin.formation.type." . Formation::TYPE_PRESENTIELLE => Formation::TYPE_PRESENTIELLE,
            "admin.formation.type." . Formation::TYPE_ELEARNING => Formation::TYPE_ELEARNING,
            "admin.formation.type." . Formation::TYPE_POWERPOINT => Formation::TYPE_POWERPOINT,
            "admin.formation.type." . Formation::TYPE_SEDD => Formation::TYPE_SEDD,
            "admin.formation.type." . Formation::TYPE_CONGRES => Formation::TYPE_CONGRES,
            "admin.formation.type." . Formation::TYPE_VIGNETTE => Formation::TYPE_VIGNETTE,
            "admin.formation.type." . Formation::TYPE_VIGNETTE_AUDIT => Formation::TYPE_VIGNETTE_AUDIT,
        );

        if ($guidedTypeOrDisplayNew) {
            $sessionTypes["admin.formation.type." . Formation::TYPE_VFC] = Formation::TYPE_VFC;
            $sessionTypes["admin.formation.type." . Formation::TYPE_TCS] = Formation::TYPE_TCS;
        }

        return $sessionTypes;
    }

    public static function getFormTypes($prefix = "admin.formation.form_type.") {
        return array (
            $prefix . Formation::FORM_TYPE_AUDIT => Formation::FORM_TYPE_AUDIT,
            $prefix . Formation::FORM_TYPE_PREDEFINED => Formation::FORM_TYPE_PREDEFINED,
            $prefix . Formation::FORM_TYPE_SURVEY => Formation::FORM_TYPE_SURVEY,
            $prefix . Formation::FORM_TYPE_VIGNETTE => Formation::FORM_TYPE_VIGNETTE,
            $prefix . Formation::FORM_TYPE_VIGNETTE_AUDIT => Formation::FORM_TYPE_VIGNETTE_AUDIT,
            $prefix . Formation::FORM_TYPE_AUDIT_VIGNETTE => Formation::FORM_TYPE_AUDIT_VIGNETTE,
            $prefix . Formation::FORM_TYPE_TCS => Formation::FORM_TYPE_TCS,
        );
    }

    /**
     * @return array
     */
    public static function getCategories()
    {
        $categories = array_keys(self::getSpecialities());
        return array_combine($categories, $categories);
    }

    /**
     * @return array
     */
    public static function getFlatSpecialities()
    {
        return call_user_func_array('array_merge', array_values(self::getSpecialities()));
    }

     /**
     * @return array
     */
    public static function getGroupes()
    {
        $specialites = self::getSpecialities();

        foreach($specialites["Médecin"] as $key => $value) {
            $specialites["Médecin"][$key] = $specialites["Médecin"][$key] === "Médecine générale" ? "Médecin généraliste" : "Médecin spécialiste";
        }

        foreach($specialites["Pharmacien"] as $key => $value) {
            $specialites["Pharmacien"][$key] = "Pharmacien";
        }
        foreach($specialites["Chirurgien-dentiste"] as $key => $value) {
            $specialites["Chirurgien-dentiste"][$key] = "Dentiste";
        }

        foreach($specialites["Infirmier"] as $key => $value) {
            $specialites["Infirmier"][$key] = "Infirmier";
        }

        foreach($specialites["Biologiste"] as $key => $value) {
            $specialites["Biologiste"][$key] = "Biologiste";
        }

        foreach($specialites["Etudiant"] as $key => $value) {
            $specialites["Etudiant"][$key] = "Etudiant";
        }

        $specialites["Manipulateur d’électroradiologie médicale (ERM)"]["Manipulateur d’électroradiologie médicale (ERM)"] = "Manipulateur d’électroradiologie médicale";
        $specialites["Technicien de laboratoire médical"]["Technicien de laboratoire médical"] = "Technicien de laboratoire";

        return $specialites;
    }

    public static function getSpecialityGroup($speciality) {
        $groups = self::getGroupes();
        foreach ($groups as $group) {
            if (isset($group[$speciality])) {
                return $group[$speciality];
            }
        }
        return null;
    }

    /**
     * @return array
     */
    public static function getFlatGroupes()
    {
        $groupes = [];

        foreach (self::getGroupes() as $specialities) {
            foreach ($specialities as $group) {
                $groupes[] = $group;
            }
        }

        return array_values(array_unique($groupes));
    }

    public static function groupProfessionAndSpecialities(): array
    {
        $groupSpecialites = self::getGroupSpecialities(true);
        $groupProfession = [
            'Médecin' => ['Médecin'],
            'Infirmier' => ['Infirmier'],
            'Masseur-kinésithérapeute' => ['Masseur-kinésithérapeute'],
            'Pharmacien' => ['Pharmacien'],
            'Préparateur en pharmacie' => ['Préparateur en pharmacie'],
            'Sage-Femme' => ['Sage-Femme'],
            'Dentiste' => ['Chirurgien-dentiste'],
            'Biologiste' => ['Biologiste'],
            'Autre' => ['Aide-soignant', 'Audioprothésiste', 'Diététicien', 'Ergothérapeute', 'Opticien-lunetier', 'Orthésiste', 'Orthopédiste-orthésiste', 'Orthophoniste', 'Orthoprothésiste', 'Orthoptiste', 'Pédicure-podologue', 'Podo-orthésiste', 'Prothésiste', 'Psychomotricien']
        ];

        $groupes = [];
        foreach ($groupProfession as $keyGroupProfession => $arrayGroupProfession) {
            $elem = [];
            $elem['groupeProfession'] = $keyGroupProfession;
            $elem['professions'] = [];
            foreach ($arrayGroupProfession as $profession) {
                $tmp = [
                    'libelle' => $profession,
                    'groupeSpecialites' => [],
                ];
                foreach ($groupSpecialites[$profession] as $libelle => $specialites) {
                    $tmp['groupeSpecialites'][] = [
                        'libelle' => $libelle,
                        'specialites' => $specialites,
                    ];
                }
                $elem['professions'][] = $tmp;
            }
            $groupes[] = $elem;
        }
        return $groupes;
    }
}
