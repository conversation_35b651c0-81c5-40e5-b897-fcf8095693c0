{% extends 'admin/base.html.twig' %}

{% block title %}{{ "admin.formation.titles.communication"|trans }}{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
<div class="box box-solid box-primary">
    <div class="box-header">
        <h3 class="box-title">{{ "admin.formation.titles.searchSession"|trans }}</h3>
    </div>
    <div class="box-body eduprat-search">
        {{ form_start(search_form, {'attr': {'class': 'form-inline'}}) }}
        <div class="row">
            <div class="col-sm-12">
                <div class="form-inline">
                
                    <div class="form-group mrm">
                        {{ form_label(search_form.key) }}<br>
                        {{ form_widget(search_form.key) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.nbSession) }}<br>
                        {{ form_widget(search_form.nbSession) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.start) }}<br>
                        {{ form_widget(search_form.start) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.end) }}<br>
                        {{ form_widget(search_form.end) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.presence) }}<br>
                        {{ form_widget(search_form.presence) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.virtualUnrelated) }}<br>
                        {{ form_widget(search_form.virtualUnrelated) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.elearningUnrelated) }}<br>
                        {{ form_widget(search_form.elearningUnrelated) }}
                    </div>
                        
                    <br>
                    
                    <div class="form-group mrm">
                        {{ form_label(search_form.categories) }}<br>
                        {{ form_widget(search_form.categories) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.specialities) }}<br>
                        {{ form_widget(search_form.specialities) }}
                    </div>

                    <br>

                    <div class="form-group mrm">
                        {{ form_label(search_form.regions) }}<br>
                        {{ form_widget(search_form.regions) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.departements) }}<br>
                        {{ form_widget(search_form.departements) }}
                    </div>

                    <br>

                    <div class="form-group mrm">
                        {{ form_label(search_form.thematiques) }}<br>
                        {{ form_widget(search_form.thematiques) }}
                    </div>

                    <div class="form-group mrm">
                        {{ form_label(search_form.prisesEnCharge) }}<br>
                        {{ form_widget(search_form.prisesEnCharge) }}
                    </div>

                    <br>

                    <div class="pull-right">
                        {% if search_form|length > 2 %} <br>{% endif %}
                        <button id="search_form_rechercher" class="btn btn-eduprat" type="submit">
                            <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {{ form_end(search_form) }}
        <div class="pull-right">
            <p>Nombre de selection : {{ selectionsFormation|length }}</p>
            {{ form_start(search_reinit_form, {'attr': {'style': 'display:inline-block;'}}) }}
                <button class="btn btn-eduprat viderSelection" type="submit">
                    <span class="fa fa-trash-o"></span> {{ "admin.plaquette.reinit"|trans }}
                </button>
                {{ form_widget(search_reinit_form._token) }}
            {{ form_end(search_reinit_form, {render_rest: false}) }}
            {{ form_start(search_add_form, {'attr': {'style': 'display:inline-block;'}}) }}
                <button id="btnAddRequest" {% if not selectionsFormation or selectionsFormation.isLastSelectionEmpty %}disabled="disabled"{% endif%} class="btn btn-eduprat" type="submit">
                    <span class="fa fa-plus"></span> {{ "admin.plaquette.addRequest"|trans }}
                </button>
                {{ form_widget(search_add_form._token) }}
            {{ form_end(search_add_form, {render_rest: false}) }}
        </div>
    </div>
</div>
<div class="box box-info">
    <div class="box-body">
        <div class="box box-solid">
            <h1>Liste des sessions</h1>
            {% if sessionsAlreadyInclude|length %}<div style="display:flex; margin-bottom: -1em;"><div class="already-include small-version"></div><div class="already-include-legend">Session déjà inclue dans une précédente recherche de la sélection</div></div>{% endif %}
            <hr style="margin-bottom: 0.5rem;">
            <div class="mbs row">
                <div class="col-sm-9">
                    <button class="btn btn-eduprat btn-eduprat--light" id="unselectAll">
                        <span class="fa fa-eraser"></span> {{ "admin.global.unselectAll"|trans }}
                    </button>
                    <button class="btn btn-eduprat btn-eduprat--light" id="selectAll">
                        <span class="fa fa-save"></span> {{ "admin.global.selectAll"|trans }}
                    </button>
                    {{ form_start(clear_current_search_form, {"attr": {"class": 'inline-block'}}) }}
                        <button class="btn btn-eduprat btn-eduprat--light">
                            <span class="fa fa-trash-o"></span> {{ "admin.plaquette.clearCurrentRequest"|trans }}
                        </button>
                    {{ form_row(clear_current_search_form._token) }}
                    {{ form_end(clear_current_search_form, {"render_rest": false}) }}
                </div>
                <div class="col-sm-3">
                    <button class="btn btn-eduprat btn-open-modale btn-selection" style="float: right;" {% if nbSessionSelection == 0 %}disabled="disabled"{% endif %} title="voir selection">
                        Voir la selection (<span class="spanNbSelectionSession">{{ nbSessionSelection }}</span>)
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                {% if formations is not null %}
                    {% if formations|length %}
                    <table id="searchResults" class="table table-bordered">
                        <thead>
                        <tr>
                            <th></th>
                            <th>{{ "admin.formation.list.presence"|trans }}</th>
                            <th>{{ "admin.formation.list.reference"|trans }}</th>
                            <th>{{ "admin.formation.list.sessionNumber"|trans }}</th>
                            <th>{{ "admin.formation.participantCount"|trans }}</th>
                            <th>{{ "admin.formation.list.title"|trans }}</th>
                            <th>{{ "admin.formation.list.startDate"|trans }}</th>
                            <th>Communication</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for formation in formations %}
                            <tr class="leftBordered">
                                
                                <td>{% if formation.id not in sessionsAlreadyInclude %}<input type="checkbox" id="session-{{ formation.id }}" data-sessionid="{{ formation.id }}" class="inclusionSession" {% if selectedOrNotSession[formation.id] is defined and selectedOrNotSession[formation.id] %}checked{% endif %}/>{% else %}<div class="already-include"></div>{% endif %}</td>
                                <td>{{ formation.programme.presence }}</td>
                                <td>{{ formation.programme.reference }}</td>
                                <td>
                                    {% if not formation.programme.isElearning %}
                                        {% if is_granted('view', formation) %}
                                            <a href="{{ path('admin_formation_show', {'id' : formation.id}) }}" target="_blank">
                                                {{ formation.sessionNumber }} <i class="fa fa-external-link" aria-hidden="true"></i>
                                            </a>
                                        {% else %}
                                            {{ formation.sessionNumber }}
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if not formation.programme.isElearning %}<b {% if formation.participations|length <= 5 %}style="color:red;"{% endif %}> {{formation.participations|length}}</b>{% endif %}
                                </td>
                                <td>{{ formation.programme.title }}</td>
                                <td>
                                    {% if not formation.programme.isElearning %}
                                        {% if is_granted('ROLE_WEBMASTER') %}
                                            <div style="display:flex">
                                                {% if formation.commentaireNotif %} <i class="fa fa-info-circle info-session info-session-blue" title="{{ formation.commentaireNotif }}"></i>{% endif %}
                                                <div>
                                                    {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
                                                        {{ formation.startDate | date('d/m/Y') }}
                                                    {% else %}
                                                        Du {{ formation.startDate | date('d/m/Y') }}<br> au {{ formation.endDate | date('d/m/Y') }}
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% else %}
                                            {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
                                                {{ formation.startDate | date('d/m/Y') }}
                                            {% else %}
                                                Du {{ formation.startDate | date('d/m/Y') }}<br> au {{ formation.endDate | date('d/m/Y') }}
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-eduprat btn-download-file" data-type="Plaquette" data-nomDoc="{{ formation.programme.title }}{% if not formation.programme.isElearning %} s{{ formation.sessionNumber }}{% endif %}" data-href="{{ url('pdf_invitation_pdf', {'id': formation.id, 'token': formation.token, 'user': app.user.id, 'tokenUser': app.user.token }) }}" title="plaquette invitation">Plaquette d'invitation</button>
                                    <button class="btn btn-eduprat btn-flyer btn-download-file" data-type="Flyer" data-nomDoc="{{ formation.programme.title }}{% if not formation.programme.isElearning %} s{{ formation.sessionNumber }}{% endif %}" data-href="{{ url('pdf_flyer_pdf', {'id': formation.id, 'token': formation.token, 'user': app.user.id, 'tokenUser': app.user.token }) }}" title="flyer">Flyer</button>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                        <div class="callout callout-info">
                            <p>
                                <span class="icon fa fa-info-circle"></span>
                                {{ 'admin.formation.empty'|trans }}
                            </p>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
        <div class="row">
            <div class="col-sm-3"></div>
            <div class="col-sm-6">
                {% if npages is defined and npages > 1 %}
                    {% include '@AlienorApi/Includes/paginationBootstrap.html.twig' with {urlName : app.request.get('_route'), extraParams : search.params } %}
                {% endif %}
            </div>
            <div class="col-sm-3 text-left" >
                <button class="btn btn-eduprat btn-selection btn-open-modale" {% if nbSessionSelection == 0 %}disabled="disabled"{% endif %} id="openSelection"  title="voir selection">Voir la selection (<span class="spanNbSelectionSession">{{ nbSessionSelection }}</span>)</button>
            </div>
        </div>
        <div class="row sub-page-plaquette">
            <div class="col-sm-7">
                <h4>Mes derniers documents générés</h4>
                <table id="downloadFilesTable" class="table">
                    <thead>
                        <tr>
                            <th>Date de la génération</th>
                            <th>Document</th>
                            <th>Type</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for downloadedPlaquetteFile in downloadPlaquetteFiles %}
                            <tr class="divDpf">
                                <td>{{ downloadedPlaquetteFile.creationDate|date('d/m/Y H:i') }}</td>
                                <td>{{ downloadedPlaquetteFile.originalName }} <a href="{{ downloadedPlaquetteFile.directLink }}" target="_blank">Télécharger</a></td>
                                <td>{% if downloadedPlaquetteFile.type == 'invitation' %}Plaquette{% else %}{{ downloadedPlaquetteFile.type|capitalize }}{% endif %}</td>
                                <td>
                                    {{ form_start(delete_dpf_form[loop.index0], {'attr': {'class': 'form-inline'}}) }}
                                    <button class="btn deleteDpf btn-danger" type="submit">
                                        <span class="fa fa-trash-o"></span>
                                    </button>
                                    {{ form_widget(delete_dpf_form[loop.index0]._token) }}
                                    {{ form_end(delete_dpf_form[loop.index0], {render_rest: false}) }}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="col-sm-5 favorites-block">
                <h4>Mes recherches favorites</h4>
                <p class="nbFavoriPhaseSpan {% if favoris|length >= constant('Eduprat\\DomainBundle\\Services\\PlaquetteManager::NB_LIMITE_FAVORI') %}text-danger{% endif %}">
                    Vous avez enregistré <span class="nbFavoriText">{{ favoris|length }} favori{% if favoris|length > 1 %}s{% endif %}</span>
                    sur les {{ constant("Eduprat\\DomainBundle\\Services\\PlaquetteManager::NB_LIMITE_FAVORI") }} possibles.
                </p>
                <table class="table">
                    <thead>
                        <th>Date de la création</th>
                        <th></th>
                        <th></th>
                    </thead>
                    <tbody>
                    {% for favori in favoris %}
                        <tr class="divFavori" data-favori="{{ favori.id }}" data-nomfavori="{{ favori.nomFavori }}">
                            <td>
                                {{ favori.dateAjoutFavori|date('d/m/Y') }}
                            </td>
                            <td>
                                <button class="btn btn-eduprat openFavori">
                                    {{ favori.nomFavori }}
                                </button>
                            </td>
                            <td>
                                {{ form_start(delete_favoris_form[loop.index0], {'attr': {'class': 'form-inline'}}) }}
                                <button class="btn deleteFavori btn-danger" type="submit">
                                    <span class="fa fa-trash-o"></span>
                                </button>
                                {{ form_widget(delete_favoris_form[loop.index0]._token) }}
                                {{ form_end(delete_favoris_form[loop.index0], {render_rest: false}) }}
                            </td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
        <div id="selectLogoModal" class="modal-plaquette modal-plaquette-logo-select">
            <div class="modal-plaquette-content modal-plaquette-logo-select-content position-relative">
                <div style="padding-top: 55px; background-color: #009199; height: 12%; color:white;">
                    <span class="close" style="color: white;line-height: 1.4; margin-right: 15px;">&times;</span>
                    <h4 class="mb" style="margin-left:0.5em; font-weight: 600;">Séléctionner le logo d'un partenaire</h4>
                </div>

                <div style="padding: 20px">
                    <div style="padding-top: 20px">
                        <div class="text-center">
                            <input id="searchLogo" style="width: 100%; height: 40px" type="text" placeholder="Rechercher un partenaire">
                        </div>
                        <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 20px;">
                            <p>Choisir un logo récemment ajoutés partout en France</p>
{#                            <p>Afficher +</p>#}
                        </div>
                    </div>
                    <div class="logos-partenaire-list">
                        {% for logo in logosPartenaire %}
                            <div>
                                <div class="logo-partenaire" data-logoId="{{ logo.id }}" data-logoName="{{ logo.logoName }}">
                                    <img src="{{ asset(logo.relativeUrl) }}" alt="{{ logo.logoName }}">
                                </div>
                                {{ logo.logoName }}
                            </div>
                        {%  endfor %}
                            <div>
                                <div class="logo-partenaire" data-logoId="0" data-logoName="Aucun logo sélectionné">

                            </div>
                            Aucun logo
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <p style="font-weight: bold;">Ou ajouter un logo partenaire</p>
                        <p style="font-size: 12px;color:grey;">Attention, seuls les logos d’établissement de santé, association, CPTS, maison de santé sont
                            acceptés</p>
                        <a href="{{ path('admin_logo_partenaire_create') }}" target="_blank" class="btn btn-eduprat btn-sm">
                            <i class="fa fa-plus"></i> {{ 'admin.global.add'|trans }}
                        </a>
                        <button id="toggleAjoutLogoForm" class="btn btn-eduprat btn-sm" style="margin-left: 10px;">
                            <i class="fa fa-plus"></i> Ajouter rapidement
                        </button>
                        <div id="ajout-logo-partenaire" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
                            <h5 style="margin-bottom: 15px;">Ajouter un nouveau logo partenaire</h5>
                            <form id="logoPartenaireAjaxForm" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label for="ajax_logoName">Nom du partenaire</label>
                                    <input type="text" id="ajax_logoName" name="logoName" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="ajax_file">Fichier image (JPEG, PNG)</label>
                                    <input type="file" id="ajax_file" name="file" class="form-control" accept="image/jpeg,image/png" required>
                                    <small class="help-block">Formats acceptés : JPEG, PNG</small>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-eduprat">
                                        <i class="fa fa-save"></i> Créer le logo
                                    </button>
                                    <button type="button" id="cancelAjoutLogo" class="btn btn-default" style="margin-left: 10px;">
                                        <i class="fa fa-times"></i> Annuler
                                    </button>
                                </div>
                                <div id="ajaxLogoMessages" style="margin-top: 10px;"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="downloadModal" class="modal-plaquette modal-plaquette-download">
            <div class="modal-plaquette-content modal-plaquette-download-content position-relative">
                <span class="close">&times;</span>
                <h4 class="mb" style="margin-left:0.5em">Donner un nom à mon téléchargement</h4>
                {{ form_start(download_plaquette_file_form) }}
                {% include 'admin/plaquette/formDownload.html.twig' with {'download_plaquette_file_form': download_plaquette_file_form} %}
                {{ form_end(download_plaquette_file_form) }}
                <div id="loader-download" class="text-center" style="display: none; position:absolute; top:0; left:0;
                        align-items: center; justify-content: center; width:100%; height:100%; background-color: rgba(211,211,211,0.50);">
                    <i class="fa fa-spinner fa-spin fa-fw fa-5x"></i>
                </div>
            </div>
        </div>
        <div id="favModal" class="modal-plaquette modal-plaquette-fav">
            <div class="modal-plaquette-content modal-plaquette-content-fav">
                <span class="close">&times;</span>
                <h4 class="mb" style="margin-left:0.5em">Enregistrer une sélection</h4>
                {{ form_start(favori_form) }}
                <div style="width:75%; margin: auto;">
                    <label class="control-label required">Nom du favori</label>
                    <span class="required" title="Ce champ est obligatoire">*</span>
                    <span>(50 caractères maximum)</span>
                    <div style="display:flex;">
                        {{ form_widget(favori_form.nomFavori) }}
                        {{ form_widget(favori_form.envoyer) }}
                    </div>
                    <div id="divFavSaveAs" style="display:flex;">
                        {{ form_widget(favori_form.nomFavori2) }}
                        {{ form_widget(favori_form.enregistrer_sous) }}
                    </div>
                    <p class="error" style="color: red;"></p>
                </div>
                {{ form_end(favori_form) }}
            </div>
        </div>
        <div id="selectionModal" class="modal-plaquette">
            <div class="modal-plaquette-content">
                <span class="close">&times;</span>
                <div class="row" style="clear: right; margin-top:0.5em;">
                    <div class="col-sm-8">
                        <h2>
                            Votre Recherche :
                        </h2>
                        <p id="titreSelection"></p>
                    </div>
                    <div class="col-sm-4" style="text-align:right;">
                        <span class="spanNbSelectionSession spanNbSelectionSession--blue strong">{{ nbSessionSelection }}</span> <span class="spanNbSelectionSession-texte">session sélectionnée</span><br>
                        <div style="display:flex; flex-direction: row; font-size: 18px; justify-content: end; margin-top: 5px;">
                            <button style="margin-right: 0.5em;" class="btn btn-open-fav-modale btn-eduprat" title="Sauvegarder la sélection"><i class="fa fa-floppy-o" aria-hidden="true"></i> {{ 'admin.plaquette.ajouterFavoris'|trans }}</button>
                            {{ form_start(search_reinit_form2) }}
                            <button class="btn btn-red viderSelection" type="submit" title="Vider la sélection"><i class="fa fa-trash-o" aria-hidden="true"></i>
                                {{ 'admin.global.delete'|trans }}</button>
                            {{ form_widget(search_reinit_form2._token) }}
                            {{ form_end(search_reinit_form2, {render_rest: false}) }}
                        </div>
                    </div>
                </div>
                <div class="row" style="clear: right">
                    <div class="col-sm-12">
                        <div id="filtersRappel">
                        </div>
                        <div id="tableSessionParent" class="hidden">
                            <table class="table table-bordered modal-plaquette-table">
                                <thead>
                                <tr>
                                    <th>{{ "admin.formation.list.reference"|trans }}</th>
                                    <th>{{ "admin.formation.list.presence"|trans }}</th>
                                    <th>{{ "admin.formation.list.sessionNumber"|trans }}</th>
                                    <th>{{ "admin.formation.participantCount"|trans }}</th>
                                    <th>{{ "admin.formation.list.title"|trans }}</th>
                                    <th>{{ "admin.formation.list.startDate"|trans }}</th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody id="selectionList"></tbody>
                            </table>
                        </div>
                        <div id="loader" class="text-center" style="margin-top: 5rem;margin-bottom: 5rem;">
                            <i class="fa fa-spinner fa-spin fa-fw fa-4x" style></i>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div style="display:flex;flex-direction: row-reverse; padding-right: 0.5em;">
                            <button id="btn-flyer" class="btn btn-eduprat btn-flyer btn-download-file modal-btn mbs" data-type="Flyer" data-href="{{ url('pdf_flyer_pdf', {'id': -1, 'token': 'token', 'user': app.user.id, 'tokenUser': app.user.token }) }}" title="flyer">{{ "admin.plaquette.generateFlyer"|trans }}</button>
                            <button id='progGenButton' style="margin-right:0.5em" class="btn btn-eduprat btn-programme modal-btn mbs" data-type="Programme" data-href="{{ url('pdf_programmes_pdf', {'id': app.user.id, 'favori' : 0 }) }}" title="programme">{{ "admin.plaquette.generateProgramme"|trans }}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script>
        $(document).ready(function() {
        	$('.btn-click-on-load').click();
			$('#eduprat_search_categories').multiSelect({});
			$('#eduprat_search_specialities').multiSelect({});
            $('#eduprat_search_prisesEnCharge').multiSelect({});
            $('#eduprat_search_regions').multiSelect({});
            $('#eduprat_search_departements').multiSelect({});
            $('#eduprat_search_thematiques').multiSelect({});
			updateSpecialities();

			$('#eduprat_search_categories').change(updateSpecialities);

			function updateSpecialities() {
				var selected = $('#eduprat_search_categories').find('option:selected').map(function (i, n) {
					return $(n).text();
				}).get();
				$.each($('#ms-eduprat_search_specialities').find('li.ms-optgroup-container'), function (index, value) {
					var $li = $(value);
					var value = $li.find('ul.ms-optgroup').find('.ms-optgroup-label').find('span').text();
					if (selected.indexOf(value) === -1) {
						$li.addClass('hidden');
						$('#eduprat_search_specialities').multiSelect('deselect', $('#eduprat_search_specialities').find('optgroup[label="'+value+'"]').find('option').map(function (i, n) {
							return $(n).attr('value');
						}).get());
					} else {
						$li.removeClass('hidden');
					}
				});
			}
		});

        var modalLogo = document.getElementById("selectLogoModal");
        $('.logo-partenaire').on('click', function() {
            const id = $(this).data('logoid');   // Attention : tout en minuscules dans l'attribut HTML
            const name = $(this).data('logoname');

            $('#eduprat_download_file_logoPartenaire').val(id);
            $('#logoTxtName').text(name);

            selectLogoModal.style.display = "none";
        });

        $('#searchLogo').on('input', function () {
            const searchTerm = $(this).val().toLowerCase();

            $('.logo-partenaire').each(function () {
                const name = $(this).data('logoname').toLowerCase();

                if (name.includes(searchTerm)) {
                    $(this).parent().show();
                } else {
                    $(this).parent().hide();
                }
            });
        });


        let inputSelect = document.querySelectorAll('form[name="eduprat_search"] select, form[name="eduprat_search"] input');
        let form_search_submit = document.getElementById('search_form_rechercher');
        let lockForm = function (e) {
            for (var i = 0; i < inputSelect.length; i++) {
                inputSelect[i].disabled = true;
            }
            form_search_submit.disabled = true;
            $('#eduprat_search_categories, #eduprat_search_specialities, #eduprat_search_prisesEnCharge, #eduprat_search_regions, #eduprat_search_departements, #eduprat_search_thematiques').multiSelect('refresh');
        };
        let resetFormSearch = function (e) {
            for (var i = 0; i < inputSelect.length; i++) {
                inputSelect[i].disabled = false;
                inputSelect[i].value = "";
            }
            form_search_submit.disabled = false;
            $('#eduprat_search_categories, #eduprat_search_specialities, #eduprat_search_prisesEnCharge, #eduprat_search_regions, #eduprat_search_departements, #eduprat_search_thematiques').multiSelect('refresh');
        };
        let resetResultSearch = function () {
            if (searchResults) {
                searchResults.innerHTML = "";
            }
            if (document.querySelector('.pagination')) {
                document.querySelector('.pagination').closest('.row').remove()
            }
        };
        let searchResults = document.getElementById('searchResults');
        let resetSearch = function() {
            resetFormSearch();
            updateSelectionNumber(0);
            resetResultSearch();
        };

        let eduprat_search_key = document.getElementById('eduprat_search_key');
        function loadDataForm() {
            let isDisabled = eduprat_search_key.disabled;
            if (isDisabled) {
                for (var i = 0; i < inputSelect.length; i++) {
                    inputSelect[i].disabled = false;
                }
            }
            const form = document.querySelector('form[name="eduprat_search"]');
            const data = new URLSearchParams(new FormData(form));
            if (isDisabled) {
                lockForm();
            }
            return data;
        }

        var btnOpenModale = document.querySelectorAll(".btn-open-modale");
        var btnOpenFavModale = document.querySelector(".btn-open-fav-modale");
        let spanNbSelectionSessions = document.querySelectorAll('.spanNbSelectionSession');
        let spanNbSelectionSessionsTexte = document.querySelectorAll('.spanNbSelectionSession-texte');
        let filtersRappel = document.getElementById('filtersRappel');
        let titreSelection = document.getElementById('titreSelection');
        let updateSelectionNumber = function(nbSelectionSession) {
            if (!selectionSessionFavoriOpened) {
                spanNbSelectionSessions.forEach((spanNbSelectionSession) => {
                    spanNbSelectionSession.innerHTML = nbSelectionSession;
                });
                spanNbSelectionSessionsTexte.forEach((spanNbSelectionSessionTexte) => {
                    spanNbSelectionSessionTexte.innerHTML = nbSelectionSession >= 2 ? 'sessions sélectionnées' : 'session sélectionnée';
                });
                btnOpenModale.forEach(function (btn) {
                    btn.disabled = nbSelectionSession == 0;
                });
            }
            updateModalCount(nbSelectionSession);
        }

        let updateSelection = function(formations, sessionIdsList) {
            var documentFragment = document.createDocumentFragment();
            formations.forEach(function(search, index) {
                search.forEach(function(formation) {
                    if (!sessionIdsList[formation.id]) {
                        let trClass = formation.isExcluded ? "session-exclued" : "session-inclued";
                        let reference = document.createElement('td');
                        reference.textContent = formation.reference;
                        let presence = document.createElement('td');
                        presence.textContent = formation.presence;
                        let sessionNumber = document.createElement('td');
                        if (formation.presence != "E-Learning") {
                            if (formation.userHasAccess) {
                                let aSession = document.createElement('a');
                                aSession.target="_blank";
                                aSession.href = "{{ path('admin_formation_show', {'id' : -1}) }}".replace('-1', formation.id);
                                aSession.textContent = formation.sessionNumber+' ';
                                let iSession = document.createElement('i');
                                iSession.className = "fa fa-external-link";
                                aSession.appendChild(iSession);
                                sessionNumber.appendChild(aSession);
                            } else {
                                sessionNumber.textContent = formation.sessionNumber;
                            }
                        }
                        let nbParticipants = document.createElement('td');
                        nbParticipants.textContent = formation.presence != "E-Learning" ? formation.nbParticipants+' ' : '';
                        let title = document.createElement('td');
                        title.textContent = formation.title;
                        let startDate = document.createElement('td');
                        startDate.textContent = formation.presence != "E-Learning" ? formation.startDate : '';
                        let icon = formation.isExcluded ? "fa-plus" : "fa-minus";
                        let isExcluded = document.createElement('td');
                        isExcluded.style = "background-color:white;";
                        let checkbox = document.createElement('input');
                        checkbox.type = "checkbox";
                        checkbox.dataset.sessionid = formation.id;
                        checkbox.dataset.token = formation.token;
                        checkbox.dataset.numrequest = index;
                        checkbox.id = index+'-'+formation.id;
                        if (index + 1 === formations.length) {
                            checkbox.dataset.islastrequest = true;
                        }
                        checkbox.className = "inclusionSession ";
                        if (!formation.isExcluded) {
                            checkbox.setAttribute('checked', true);
                        }
                        isExcluded.appendChild(checkbox);

                        let label = document.createElement('label');
                        label.setAttribute('for', index+'-'+formation.id);
                        label.className = 'btn btn-eduprat btn-icons-incexclusion';
                        let span = document.createElement('span');
                        span.className ='fa '+ icon;
                        label.appendChild(span);
                        isExcluded.appendChild(label);
                        let tr = document.createElement('tr');
                        tr.className = trClass;
                        documentFragment.appendChild(tr);
                        tr.appendChild(reference);
                        tr.appendChild(presence);
                        tr.appendChild(sessionNumber);
                        tr.appendChild(nbParticipants);
                        tr.appendChild(title);
                        tr.appendChild(startDate);
                        tr.appendChild(isExcluded);
                        checkbox.addEventListener('change', function (e) {
                            manageAll(e);
                            toggleSelectionTr(e);
                            checkAllowNewFavori();
                        });
                        sessionIdsList[formation.id] = true;
                    }
                });
            });
            document.getElementById('selectionList').appendChild(documentFragment);
            showTableSession();
        }

        var checkboxes_session_inclusion = document.querySelectorAll('.inclusionSession');
        let manageAll = function(e) {
            lockForm(e);
            allowNewRequest();
            let isInModal = e.currentTarget.closest('#selectionModal') !== null;
            let data = isInModal ? new URLSearchParams() : loadDataForm();
            let isCheckbox = e.currentTarget.tagName == 'INPUT';
            if (!isCheckbox) {
                data.append('selectAll', e.currentTarget.selectAll);
                for (var i = 0; i < checkboxes_session_inclusion.length; i++) {
                    checkboxes_session_inclusion[i].checked = e.currentTarget.selectAll;
                }
            } else {
                data.append('session', event.target.dataset.sessionid);
                data.append('include', event.target.checked);
                if (event.target.dataset.hasOwnProperty('numrequest')) {
                    data.append('numRequest', event.target.dataset.numrequest);
                }
                if (selectionSessionFavoriOpened) {
                    hasModifiedElement = true;
                    data.append('selectionSession', selectionSessionFavoriOpened);
                }
            }
            fetch("{{ path('admin_plaquette_search_save') }}", {
                method: "POST",
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: data,
            })
            .then(response => response.json())
            .then(response => updateSelectionNumber(response.nbSessionSelection));
        };

        let toggleSelectionTr = function(e) {
            if (typeof e.currentTarget.dataset.islastrequest !== "undefined" && selectionSessionFavoriOpened === null) {
                document.getElementById("session-" + e.currentTarget.dataset.sessionid).checked = e.currentTarget.checked;
            }
            let focusedTr = e.currentTarget.closest('tr');
            let focusedSpan = e.currentTarget.nextSibling.querySelector("span");
            if (event.target.checked) {
                focusedTr.classList.add("session-inclued");
                focusedTr.classList.remove("session-exclued");
                focusedSpan.classList.remove('fa-plus');
                focusedSpan.classList.add('fa-minus');
            } else {
                focusedTr.classList.add("session-exclued");
                focusedTr.classList.remove("session-inclued");
                focusedSpan.classList.remove('fa-minus');
                focusedSpan.classList.add('fa-plus');
            }
        };

        let selectAll = document.getElementById('selectAll');
        selectAll.addEventListener('click', manageAll);
        selectAll.selectAll = true;

        let unselectAll = document.getElementById('unselectAll');
        unselectAll.addEventListener('click', manageAll);
        unselectAll.selectAll = false;

        for (var i = 0; i < checkboxes_session_inclusion.length; i++) {
            checkboxes_session_inclusion[i].addEventListener('click', manageAll);
        }

        let btnAddRequest = document.getElementById('btnAddRequest');
        let allowNewRequest = function() {
            btnAddRequest.disabled = false;
        };

        let viderSelectionBtns = document.querySelectorAll('.viderSelection');
        for (var i = 0; i < viderSelectionBtns.length; i++) {
            viderSelectionBtns[i].addEventListener('click', function (e) {
                e.preventDefault();
                if (window.confirm("Êtes-vous sûr de vouloir vider votre selection ?")) {
                    e.currentTarget.closest('form').submit();
                }
                return false;
            });
        }

        // Gestion de la modal
        var modal = document.getElementById("selectionModal");
        var loader_modal = document.getElementById("loader");
        var tableSessionParent = document.getElementById("tableSessionParent");
        var favori_envoyer = document.querySelectorAll(".saveFavori");
        var favoris_remove = document.querySelectorAll(".deleteFavori");
        var dpf_remove = document.querySelectorAll(".deleteDpf");
        var favoris_open = document.querySelectorAll(".openFavori");
        var selectionModalClose = document.querySelector("#selectionModal .close");
        var favModalClose = document.querySelector("#favModal .close");
        let selectionSessionFavoriOpened = null;
        let hasModifiedElement = false; // permet de savoir si des modifications sont en cours sur le favori sélectionné
        var modalFav = document.getElementById("favModal");
        let btn_flyer = document.getElementById('btn-flyer');
        let nomFavInput = document.getElementById('eduprat_domainbundle_selectionsession_favori_nomFavori');
        let nomFavInput2 = document.getElementById('eduprat_domainbundle_selectionsession_favori_nomFavori2');
        let divFavSaveAs = document.getElementById('divFavSaveAs');
        let buttonSaveFav = document.getElementById('eduprat_domainbundle_selectionsession_favori_envoyer');
        let buttonSaveAsFav = document.getElementById('eduprat_domainbundle_selectionsession_favori_enregistrer_sous');
        let viderSelectionModal = document.querySelector("#selectionModal .viderSelection");
        let progGenButton = document.getElementById("progGenButton");
        let progGenDefaultUrl = document.getElementById("progGenButton").dataset.href;
        let NB_MAX_FAVORIS = {{ constant("Eduprat\\DomainBundle\\Services\\PlaquetteManager::NB_LIMITE_FAVORI") }};

        var selectionLogoModalClose = document.querySelector("#selectLogoModal .close");
        var openLogoModal = document.querySelector("#openLogoModal");

        var hideTableSession = function() {
            tableSessionParent.classList.add("hidden");
            loader_modal.classList.remove("hidden");
            document.querySelector("#selectionModal .spanNbSelectionSession").innerHTML = '<i class="fa fa-spinner fa-spin fa-fw"></i>';
        };

        var showTableSession = function() {
            tableSessionParent.classList.remove('hidden');
            loader_modal.classList.add('hidden');
        };

        let checkAllowNewFavori = function () {
            // si creation d'une nouvelle selection alors le nombre de favoris doit être inférieur à NB_MAX_FAVORIS (20)
            if (selectionSessionFavoriOpened === null) {
                btnOpenFavModale.disabled = isLimitNbFavori();
            } else {
                // si édition de favori, on attend une modif de la selection pour autoriser ouverture de la modale d'enregistrement
                btnOpenFavModale.disabled = !hasModifiedElement;
                // le bouton "Enregistrer sous" n'est disponible que si l'on n'a pas atteint NB_MAX_FAVORIS (20)
                buttonSaveAsFav.disabled = isLimitNbFavori();
            }
        };

        // le nombre de favoris doit être inférieur à NB_MAX_FAVORIS (20)
        let isLimitNbFavori = function() {
            return document.querySelectorAll(".table .divFavori").length >= NB_MAX_FAVORIS;
        }

        // affiche la modale récapitulant les sessions selectionnées
        var loadModal = function (selectionSessionFavori = null, favName = null) {
            selectionSessionFavoriOpened = selectionSessionFavori;
            modal.style.display = "block";
            hideTableSession();
            viderSelectionModal.style.display = selectionSessionFavoriOpened === null ? 'inline-block' : 'none';
            checkAllowNewFavori();
            let route = "{{ path('admin_plaquette_currentSelection', {'selectionSession' : null}) }}";
            if (selectionSessionFavori != null) {
                route += '/'+selectionSessionFavori;
                progGenButton.dataset.href = progGenDefaultUrl.replace("/0", "/"+selectionSessionFavori)
                document.getElementById('eduprat_download_file_originalName').value = favName;
            } else {
                document.getElementById('eduprat_download_file_originalName').value = "";
            }
            fetch(route, {
                method: "POST",
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
            })
            .then(response => response.json())
            .then(function(response) {
                updateSelection(response.formations, response.sessionIdsList);
                updateModalCount(response.count);
                updateModalFilterList(response.filters);
                titreSelection.innerText = response.title;
            });
        }

        // affiche la modale pour sauvegarder le favori
        var loadFavModal = function () {
            modalFav.style.display = "block";
            if (selectionSessionFavoriOpened) {
                nomFavInput.disabled = true;
                nomFavInput.value = document.querySelector("tr.divFavori[data-favori='"+selectionSessionFavoriOpened+"']").getAttribute('data-nomfavori');
                divFavSaveAs.classList.remove('hidden');
                nomFavInput2.value = nomFavInput.value + ' - copie';
                buttonSaveFav.textContent = 'Mettre à jour';
            } else {
                divFavSaveAs.classList.add('hidden');
                buttonSaveFav.textContent = 'Enregistrer';
                if (nomFavInput.value === '') {
                    const d = new Date();
                    nomFavInput.value = 'Favori-'+("0" + d.getDate()).slice(-2)+(("0" + (d.getMonth() + 1)).slice(-2))+d.getFullYear();
                }
            }
        }

        btnOpenFavModale.onclick = function(e) {
            e.preventDefault();
            loadFavModal(e);
            return false;
        }

        for (var i = 0; i < btnOpenModale.length; i++) {
            btnOpenModale[i].onclick = function() {
                loadModal();
            }
        }
        // évenement click sur le nom d'un favori -> ouverture de la modale correspondante
        for (var i = 0; i < favoris_open.length; i++) {
            favoris_open[i].addEventListener('click', function(e) {
                e.preventDefault();
                loadModal(e.currentTarget.closest('.divFavori').dataset.favori, e.currentTarget.closest('.divFavori').dataset.nomfavori);
                return false;
            });
        }

        let closeSelectionModale = function() {
            progGenButton.dataset.href = progGenDefaultUrl;
              if (selectionSessionFavoriOpened && hasModifiedElement) {
                let route = "{{ path('plaquette_delete_favori_temporaire', {'parentSelectionFormation' : -1}) }}";
                if (selectionSessionFavoriOpened != null) {
                    route = route.replace('-1', selectionSessionFavoriOpened);
                }
                if (confirm('Êtes-vous sûr d\'abandonner les modifications du favori ?')) {
                    fetch(route, {
                        method: "POST",
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                    });
                } else {
                    return;
                }
            }
            modal.style.display = "none";
            resetModal();
        }
        let closeFavModal = function() {
            modalFav.style.display = "none";
        }

        selectionLogoModalClose.onclick = function() {
            selectLogoModal.style.display = "none";
        }

        openLogoModal.onclick = function() {
            selectLogoModal.style.display = "block";
        }

        selectionModalClose.onclick = function() {
            closeSelectionModale();
            document.getElementById('eduprat_download_file_originalName').value = "";
        }

        favModalClose.addEventListener('click', closeFavModal);

        window.onclick = function(event) {
            if (event.target == modal) {
                closeSelectionModale();
            }
        }

        let updateModalCount = function (count) {
            let currentCount = null;
            if (document.querySelector("#openSelection .spanNbSelectionSession")) {
                currentCount = document.querySelector("#openSelection .spanNbSelectionSession").innerHTML;
            }
            count = count !== null && count !== false ? count : currentCount;
            document.querySelector("#selectionModal .spanNbSelectionSession").innerHTML = count;
            document.querySelector("#selectionModal .spanNbSelectionSession-texte").innerHTML = count >= 2 ? 'sessions sélectionnées' : 'session sélectionnée';
            btn_flyer.disabled = (count ? count : currentCount) > 3;
        }
        let updateModalFilterList = function (fieldsRequests) {
            var documentFragment = document.createDocumentFragment();
            for (let i = 0; i < fieldsRequests.length; i++) {
                for (const property in fieldsRequests[i]) {
                    let elem = document.createElement('span');
                    elem.className = 'tag tag--color'+ (i % 3);
                    elem.title = 'Selection n°'+(i+1);
                    if (Array.isArray(fieldsRequests[i][property])) {
                        elem.textContent = `${property} : ${fieldsRequests[i][property].join(', ')}`;
                    } else {
                        elem.textContent = `${property} : ${fieldsRequests[i][property]}`;
                    }
                    documentFragment.appendChild(elem);
                }
            }
            filtersRappel.appendChild(documentFragment);
        }

        let resetModal = function(e) {
            document.getElementById('selectionList').innerHTML = "";
            filtersRappel.innerHTML = "";
            titreSelection.innerHTML = "";
            selectionSessionFavoriOpened = null;
            hasModifiedElement = false;
        }

        let nbFavoriTextSpans = document.querySelectorAll('.nbFavoriText');
        let nbFavoriPhaseSpans = document.querySelectorAll('.nbFavoriPhaseSpan');
        let updateNbFavoriText = function () {
            let nbFav = document.querySelectorAll(".table .divFavori").length;
            for (var i = 0; i < nbFavoriTextSpans.length; i++) {
                nbFavoriTextSpans[i].textContent = nbFav+ (nbFav < 2 ? ' favori' : ' favoris');
            }
            for (var i = 0; i < nbFavoriPhaseSpans.length; i++) {
                if (nbFav >= NB_MAX_FAVORIS) {
                    nbFavoriPhaseSpans[i].classList.add('text-danger');
                } else {
                    nbFavoriPhaseSpans[i].classList.remove('text-danger');
                }
            }
        }
        let removeFavori = function(e) {
            e.preventDefault();
            if (window.confirm("Êtes-vous sûr de vouloir supprimer ce favori ?")) {
                let form = e.currentTarget.closest('form');
                const data = new URLSearchParams(new FormData(form));
                fetch(form.action, {
                    method: "POST",
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: data,
                })
                .then(response => response.json())
                .then(function (response) {
                    if (response.etat === false) {
                        alert(response.error);
                    }
                    form.closest('.divFavori').remove();
                    updateNbFavoriText();
                });
            }
            return false;
        };

        let removeDpf = function(e) {
            e.preventDefault();
            if (window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) {
                let form = e.currentTarget.closest('form');
                const data = new URLSearchParams(new FormData(form));
                fetch(form.action, {
                    method: "POST",
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: data,
                })
                .then(response => response.json())
                .then(function (response) {
                    if (response.etat === false) {
                        alert(response.error);
                    }
                    form.closest('.divDpf').remove();
                });
            }
            return false;
        };

        let addFavoriInList = function (response) {
            let tr = document.createElement('tr');
            tr.className="divFavori";
            tr.dataset.favori = response.selectionFormationId;
            tr.dataset.nomfavori = response.selectionFormationNom;
            let tdDate = document.createElement('td');
            tdDate.textContent = response.selectionFormationDate;
            let tdOpenModal = document.createElement('td');
            let buttonOpenModal = document.createElement('button');
            buttonOpenModal.className = 'btn btn-eduprat openFavori';
            buttonOpenModal.textContent = response.selectionFormationNom;
            tdOpenModal.appendChild(buttonOpenModal);
            let tdSuppression = document.createElement('td');
            tdSuppression.innerHTML = response.formSuppression;
            tr.appendChild(tdDate);
            tr.appendChild(tdOpenModal);
            tr.appendChild(tdSuppression);
            buttonOpenModal.addEventListener('click', function(e) {
                loadModal(e.currentTarget.closest('.divFavori').dataset.favori);
            });
            tdSuppression.querySelector('button.deleteFavori').addEventListener('click', function (e) {
                return removeFavori(e);
            })
            document.querySelector('.favorites-block tbody').appendChild(tr);
            updateNbFavoriText();
        }

        for (var i=0; i < favori_envoyer.length; i++) {
            favori_envoyer[i].addEventListener('click', function(e) {
                e.preventDefault();
                const data = new URLSearchParams(new FormData(e.currentTarget.closest('form')));
                if (selectionSessionFavoriOpened) {
                    data.append('parent_favori_number', selectionSessionFavoriOpened);
                }
                data.append('is_save_as', e.currentTarget.dataset.hasOwnProperty('saveas'));
                fetch("{{ path('admin_plaquette_search_convert_as_favori') }}", {
                    method: "POST",
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: data,
                })
                .then(response => response.json())
                .then(function(response) {
                    if (response.etat === false) {
                        if (typeof response.errorMessage != 'undefined') {
                            document.querySelector('.modal-plaquette-content-fav .error').textContent = response.errorMessage;
                        } else {
                            alert(response.error);
                        }
                    } else {
                        closeFavModal();
                        hasModifiedElement = false;
                        resetModal();
                        resetSearch();
                        if (response.needCompleteListFavori) {
                            addFavoriInList(response);
                        }
                        loadModal(response.selectionFormationId);
                    }
                });
                return false;
            });
        }
        for (var i = 0; i < favoris_remove.length; i++) {
            favoris_remove[i].addEventListener('click', function (e) {
                return removeFavori(e);
            });
        }

        for (var i = 0; i < dpf_remove.length; i++) {
            dpf_remove[i].addEventListener('click', function (e) {
                return removeDpf(e);
            });
        }


        /**
        * Téléchargement
        */
        var downloadModalClose = document.querySelector("#downloadModal .close");
        let loaderDownload = document.getElementById('loader-download');
        let closeDownloadModal = function() {
            modalDownload.style.display = "none";
        }
        let loadDownloadModal = function (target) {
            modalDownload.style.display = "block";
            document.querySelector('#downloadModal form').action = target.dataset.href;
            if (target.classList.contains('btn-flyer')) {
                if (target.classList.contains('modal-btn')) {
                    let checkboxes = document.querySelectorAll('#selectionModal .inclusionSession');
                    for (let i = 0; i < checkboxes.length; i++) {
                        if (checkboxes[i].checked) {
                            document.querySelector('#downloadModal form').action =
                                document.querySelector('#downloadModal form').action.replace('-1', checkboxes[i].dataset.sessionid);
                            document.querySelector('#downloadModal form').action =
                                document.querySelector('#downloadModal form').action.replace('token', checkboxes[i].dataset.token);
                            if (i + 1 < checkboxes.length ) {
                                document.querySelector('#downloadModal form').action +=  '/-1/token';
                            }
                        }
                    }
                }
                document.getElementById('eduprat_download_file_titre').closest('.form-group').classList.remove('hide');
                document.getElementById('eduprat_download_file_formationType').closest('.form-group').classList.remove('hide');
            } else {
                document.getElementById('eduprat_download_file_titre').closest('.form-group').classList.add('hide');
                document.getElementById('eduprat_download_file_formationType').closest('.form-group').classList.add('hide');
            }
        }
        // affiche la modale pour télécharger le fichier
        let modalDownload = document.getElementById('downloadModal');
        let modalDownloadForm = document.querySelector('form[name="eduprat_download_file"]');
        let modalDownloadFormSubmitBtn = document.getElementById('eduprat_download_file_telecharger');
        let lockFormDownload = false;
        modalDownloadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (lockFormDownload) {
                return;
            }
            lockFormDownload = true;
            loaderDownload.style.display = 'flex';
            modalDownloadFormSubmitBtn.disabled = true;
            const data = new URLSearchParams(new FormData(e.currentTarget));
            fetch(e.currentTarget.action, {
                method: "POST",
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: data,
            })
            .then(response => response.json())
            .then(function(response) {
                loaderDownload.style.display = 'none';
                modalDownloadFormSubmitBtn.disabled = false;
                if (response.etat === true) {
                    window.open(response.directLink, '_blank');
                    lockFormDownload = false;
                    // on supprime les fichiers > 10 plus vieux
                    let trsFileDownloaded = document.querySelectorAll('#downloadFilesTable tbody tr');
                    if (trsFileDownloaded.length >= 10) {
                        for (let i = 0; i <= trsFileDownloaded.length - 10; i++) {
                            trsFileDownloaded[i].remove();
                        }
                    }
                    // on ajoute le nouveau fichier dans le tableau
                    let tr = document.createElement('tr');
                    let tdDate = document.createElement('td');
                    let date = new Date();
                    let day = date.getDate();
                    let month = date.getMonth() + 1;
                    month = String(month).padStart(2, '0');
                    let year = date.getFullYear();
                    let hour = String(date.getHours()).padStart(2, '0');
                    let minutes = String(date.getMinutes()).padStart(2, '0');
                    tdDate.textContent = `${day}/${month}/${year} ${hour}:${minutes}`;
                    let tdName = document.createElement('td');
                    tdName.textContent = document.getElementById('eduprat_download_file_originalName').value+" ";
                    let a = document.createElement('a');
                    a.href = response.directLink;
                    a.target = '_blank';
                    a.textContent = "Télécharger";
                    tdName.appendChild(a);
                    let tdType = document.createElement('td');
                    // tdType.textContent = e.currentTarget.dataset.type;
                    tr.appendChild(tdDate);
                    tr.appendChild(tdName)
                    // tr.appendChild(tdType)
                    document.querySelector('#downloadFilesTable tbody').appendChild(tr);

                    // on remplace le formulaire de téléchargement
                    modalDownloadForm.innerHTML = response.download_plaquette_file_form;

                    // on ferme automatiquement la modale
                    closeDownloadModal();
                } else {
                    if (typeof response.error != 'undefined') {
                        alert(response.error);
                    } else {
                        alert('Une erreur s\'est produite lors de la génération du pdf. Merci de réessayer');
                    }
                    if (typeof response.download_plaquette_file_form != 'undefined') {
                        modalDownloadForm.innerHTML = response.download_plaquette_file_form;
                    }
                }
            })
            .catch(error => {
                alert('Une erreur s\'est produite lors de la génération du pdf. Merci de réessayer');
                loaderDownload.style.display = 'none';
                modalDownloadFormSubmitBtn.disabled = false;
                lockFormDownload = false;
            });
            return false;
        })

        // ouverture et fermeture de la modale de téléchargement
        let btn_download_files = document.querySelectorAll('.btn-download-file');
        for (var i =0; i < btn_download_files.length; i++) {
            btn_download_files[i].addEventListener('click', function (e) {
                e.preventDefault();
                if (e.currentTarget.dataset.nomdoc != undefined) {
                    document.getElementById('eduprat_download_file_originalName').value = e.currentTarget.dataset.nomdoc;
                }
                loadDownloadModal(e.currentTarget);
                return false;
            });
        }
        progGenButton.addEventListener('click', e => loadDownloadModal(e.currentTarget));
        downloadModalClose.onclick = function() {
            closeDownloadModal();
        }
    </script>
{% endblock %}