<!DOCTYPE html>
<html>
<head>
    <title>Test Formulaire Logo Partenaire</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container" style="margin-top: 50px;">
        <h2>Test du formulaire d'ajout de logo partenaire</h2>
        
        <button id="toggleAjoutLogoForm" class="btn btn-primary btn-sm">
            <i class="fa fa-plus"></i> Ajouter rapidement
        </button>
        
        <div id="ajout-logo-partenaire" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
            <h5 style="margin-bottom: 15px;">Ajouter un nouveau logo partenaire</h5>
            <form id="logoPartenaireAjaxForm" enctype="multipart/form-data">
                <input type="hidden" name="_token" value="test_token">
                <div class="form-group">
                    <label for="ajax_logoName">Nom du partenaire</label>
                    <input type="text" id="ajax_logoName" name="eduprat_domainbundle_logo_partenaire[logoName]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="ajax_file">Fichier image (JPEG, PNG)</label>
                    <input type="file" id="ajax_file" name="eduprat_domainbundle_logo_partenaire[file]" class="form-control" accept="image/jpeg,image/png" required>
                    <small class="help-block">Formats acceptés : JPEG, PNG</small>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save"></i> Créer le logo
                    </button>
                    <button type="button" id="cancelAjoutLogo" class="btn btn-default" style="margin-left: 10px;">
                        <i class="fa fa-times"></i> Annuler
                    </button>
                </div>
                <div id="ajaxLogoMessages" style="margin-top: 10px;"></div>
            </form>
        </div>
    </div>

    <script>
        // Gestion du formulaire d'ajout de logo partenaire
        let toggleAjoutLogoBtn = document.getElementById('toggleAjoutLogoForm');
        let ajoutLogoDiv = document.getElementById('ajout-logo-partenaire');
        let cancelAjoutLogoBtn = document.getElementById('cancelAjoutLogo');
        let logoPartenaireForm = document.getElementById('logoPartenaireAjaxForm');
        let ajaxLogoMessages = document.getElementById('ajaxLogoMessages');

        // Afficher/masquer le formulaire
        toggleAjoutLogoBtn.onclick = function() {
            if (ajoutLogoDiv.style.display === 'none') {
                ajoutLogoDiv.style.display = 'block';
                toggleAjoutLogoBtn.innerHTML = '<i class="fa fa-minus"></i> Masquer le formulaire';
            } else {
                ajoutLogoDiv.style.display = 'none';
                toggleAjoutLogoBtn.innerHTML = '<i class="fa fa-plus"></i> Ajouter rapidement';
                resetLogoForm();
            }
        };

        // Annuler l'ajout
        cancelAjoutLogoBtn.onclick = function() {
            ajoutLogoDiv.style.display = 'none';
            toggleAjoutLogoBtn.innerHTML = '<i class="fa fa-plus"></i> Ajouter rapidement';
            resetLogoForm();
        };

        // Fonction pour réinitialiser le formulaire
        function resetLogoForm() {
            logoPartenaireForm.reset();
            ajaxLogoMessages.innerHTML = '';
        }

        // Fonction pour afficher les messages
        function showLogoMessage(message, type = 'success') {
            let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            ajaxLogoMessages.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        }

        // Test de soumission du formulaire
        logoPartenaireForm.onsubmit = function(e) {
            e.preventDefault();
            
            let formData = new FormData(logoPartenaireForm);
            let submitBtn = logoPartenaireForm.querySelector('button[type="submit"]');
            
            // Désactiver le bouton pendant l'envoi
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Création en cours...';
            
            // Simulation d'un appel AJAX réussi
            setTimeout(() => {
                showLogoMessage('Logo partenaire créé avec succès !', 'success');
                resetLogoForm();
                
                // Masquer le formulaire après succès
                setTimeout(() => {
                    ajoutLogoDiv.style.display = 'none';
                    toggleAjoutLogoBtn.innerHTML = '<i class="fa fa-plus"></i> Ajouter rapidement';
                }, 2000);
                
                // Réactiver le bouton
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fa fa-save"></i> Créer le logo';
            }, 1000);
        };
    </script>
</body>
</html>
